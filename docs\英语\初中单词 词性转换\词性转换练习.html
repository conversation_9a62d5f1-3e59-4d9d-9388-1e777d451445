<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初中英语词性转换练习</title>
    <style>
        /* 艾宾浩斯学习计划表样式 */
        .ebbinghaus-container {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            background: #1a1a1a;
            padding: 20px;
            box-sizing: border-box;
            margin: 0;
            font-size: 16px;
            color: #e0e0e0;
        }

        .ebbinghaus-container h1 {
            color: #f0f0f0;
            margin-bottom: 25px;
            font-weight: normal;
            border-bottom: 2px solid rgba(224,224,224,0.3);
            padding-bottom: 10px;
            text-align: center;
            font-size: 2.5em;
        }

        .ebbinghaus-container .container {
            background: #2d2d2d;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
            overflow: hidden;
            width: 100%;
            max-width: 1200px;
            border: 1px solid #404040;
        }

        .ebbinghaus-container .header {
            background: linear-gradient(45deg, #404040, #505050);
            color: #f0f0f0;
            padding: 20px;
            text-align: center;
        }

        .ebbinghaus-container .header h2 {
            font-size: 1.8em;
            margin-bottom: 10px;
            color: #f0f0f0;
        }

        .ebbinghaus-container .header p {
            font-size: 1.1em;
            opacity: 0.9;
            margin: 0;
            color: #e0e0e0;
        }

        #study-plan-setup-area {
            padding: 30px;
            background: #2d2d2d;
        }

        #draggable-numbers-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #e0e0e0;
            margin-bottom: 20px;
            text-align: center;
        }

        #draggable-numbers-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 30px;
            padding: 20px;
            background: #3a3a3a;
            border-radius: 10px;
            border: 2px dashed #555555;
        }

        .draggable-number {
            background: linear-gradient(45deg, #4a5568, #2d3748);
            color: #e0e0e0;
            padding: 12px 8px;
            border-radius: 8px;
            text-align: center;
            cursor: grab;
            user-select: none;
            transition: all 0.3s;
            font-weight: bold;
            font-size: 0.9em;
            line-height: 1.2;
            border: 1px solid #555555;
        }

        .draggable-number:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 85, 104, 0.4);
            background: linear-gradient(45deg, #5a6578, #3d4758);
        }

        .draggable-number:active {
            cursor: grabbing;
            transform: scale(0.95);
        }

        .draggable-number.clicked {
            background: linear-gradient(45deg, #48bb78, #38a169);
            animation: clickPulse 0.3s ease;
        }

        @keyframes clickPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        #study-plan-table-container {
            margin-top: 30px;
        }

        #study-plan-table-container h3 {
            text-align: center;
            color: #e0e0e0;
            font-size: 1.5em;
            margin-bottom: 20px;
        }

        #study-plan-table {
            width: 100%;
            border-collapse: collapse;
            background: #3a3a3a;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            border: 1px solid #555555;
        }

        #study-plan-table th {
            background: linear-gradient(45deg, #4a4a4a, #3a3a3a);
            color: #e0e0e0;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            font-size: 0.9em;
            border-bottom: 1px solid #555555;
        }

        #study-plan-table td {
            padding: 10px 8px;
            text-align: center;
            border-bottom: 1px solid #555555;
            vertical-align: middle;
            min-height: 40px;
            background: #3a3a3a;
            color: #e0e0e0;
        }

        #study-plan-table tr:nth-child(even) td {
            background-color: #404040;
        }

        #study-plan-table tr:hover td {
            background-color: #4a4a4a;
        }

        .plan-cell {
            min-height: 35px;
            border: 1px dashed #666666;
            border-radius: 4px;
            padding: 4px;
            background: #2d2d2d;
            position: relative;
        }

        .plan-cell.drag-over {
            background: #4a5568;
            border-color: #718096;
        }

        .plan-cell-content {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            justify-content: center;
            align-items: center;
        }

        .plan-cell-item {
            background: #4a5568;
            color: #e0e0e0;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid #666666;
        }

        .plan-cell-item:hover {
            background: #5a6578;
            transform: scale(1.05);
        }

        /* 复习单元格样式 */
        .review-cell {
            min-height: 60px;
            border: 1px solid #666666;
            border-radius: 4px;
            padding: 8px;
            background: #2d2d2d;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .review-input {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid #666666;
            border-radius: 3px;
            font-size: 0.8em;
            text-align: center;
            background: #3a3a3a;
            color: #e0e0e0;
            max-length: 6;
        }

        .review-input:focus {
            outline: none;
            border-color: #4a5568;
        }

        .complete-btn {
            padding: 2px 6px;
            font-size: 0.7em;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s;
            background: #48bb78;
            color: white;
            font-weight: bold;
        }

        .complete-btn:hover {
            background: #38a169;
        }

        .complete-btn.completed {
            background: #38a169;
            opacity: 0.8;
        }

        .review-status {
            font-size: 0.7em;
            text-align: center;
            color: #a0a0a0;
            margin-top: 2px;
        }

        .review-status.completed {
            color: #48bb78;
            font-weight: bold;
        }

        .plan-action-buttons-container {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .plan-action-buttons-container button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s;
            color: white;
        }

        #save-plan-button {
            background: linear-gradient(45deg, #48bb78, #38a169);
        }

        #save-plan-button:hover {
            background: linear-gradient(45deg, #38a169, #2f855a);
        }

        #reset-plan-button {
            background: linear-gradient(45deg, #f56565, #e53e3e);
        }

        #reset-plan-button:hover {
            background: linear-gradient(45deg, #e53e3e, #c53030);
        }

        #goto-practice-button {
            background: linear-gradient(45deg, #4a5568, #2d3748);
            font-size: 1.1em;
            padding: 15px 25px;
        }

        #goto-practice-button:hover {
            background: linear-gradient(45deg, #2d3748, #1a202c);
        }

        #print-button {
            background: linear-gradient(45deg, #718096, #4a5568);
        }

        #print-button:hover {
            background: linear-gradient(45deg, #4a5568, #2d3748);
        }

        .plan-row-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .plan-row-buttons button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            background: #6c757d;
            color: white;
            transition: background 0.3s;
        }

        .plan-row-buttons button:hover {
            background: #5a6268;
        }

        /* 折叠功能样式 */
        .collapse-toggle {
            background-color: #4a4a4a;
            border: 1px solid #666666;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 0.8em;
            color: #e0e0e0;
            font-weight: normal;
            transition: background-color 0.2s;
            min-width: 30px;
            border-radius: 3px;
        }

        .collapse-toggle:hover {
            background-color: #5a5a5a;
        }

        .collapse-toggle.collapsed {
            background-color: #6a6a6a;
        }

        .row-group-collapsed {
            display: none;
        }

        .group-header-row {
            background-color: #404040;
            font-weight: bold;
        }

        .group-header-row td {
            background-color: #404040 !important;
            border-bottom: 2px solid #666666;
            color: #f0f0f0;
        }

        /* 折叠列 */
        #study-plan-table th:nth-child(1),
        #study-plan-table td:nth-child(1) {
            min-width: 40px;
            width: 40px;
            max-width: 40px;
            text-align: center;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #1a1a1a;
            min-height: 100vh;
            padding: 20px;
            color: #e0e0e0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2d2d2d;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
            overflow: hidden;
            border: 1px solid #404040;
        }

        .header {
            background: linear-gradient(45deg, #404040, #505050);
            color: #f0f0f0;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #f0f0f0;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            color: #e0e0e0;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .mode-selector {
            display: flex;
            gap: 10px;
        }

        .mode-btn {
            padding: 12px 24px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 1.1em;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .mode-btn.active {
            background: #007bff;
            color: white;
        }

        .stats {
            display: flex;
            gap: 20px;
            font-weight: bold;
        }



        .exercise-area {
            padding: 40px;
            min-height: 400px;
            background: #2d2d2d;
            color: #e0e0e0;
        }

        .question {
            text-align: center;
            margin-bottom: 30px;
        }

        .question h2 {
            font-size: 1.8em;
            color: #e0e0e0;
            margin-bottom: 20px;
        }

        .word-display {
            font-size: 2.5em;
            color: #4a5568;
            font-weight: bold;
            margin: 20px 0;
        }

        .pos-hint {
            font-size: 1.2em;
            color: #a0a0a0;
            margin-bottom: 30px;
        }

        .series-display {
            text-align: center;
            margin: 30px 0;
        }

        .series-title {
            font-size: 2.2em;
            color: #4a5568;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .series-hint {
            font-size: 1.1em;
            color: #a0a0a0;
            margin-bottom: 20px;
        }

        .clues-area {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .clue-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #3a3a3a;
            border-radius: 8px;
            border-left: 4px solid #4a5568;
        }

        .pos-tag {
            background: #4a5568;
            color: #e0e0e0;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            min-width: 50px;
            text-align: center;
            margin-right: 15px;
            font-size: 0.9em;
        }

        .meaning {
            flex: 1;
            font-weight: 500;
            color: #e0e0e0;
            margin-right: 15px;
        }

        .clue-input {
            width: 200px;
            padding: 8px 12px;
            border: 2px solid #666666;
            border-radius: 6px;
            font-size: 1em;
            transition: border-color 0.3s;
            background: #2d2d2d;
            color: #e0e0e0;
        }

        .clue-input:focus {
            outline: none;
            border-color: #4a5568;
        }

        .clue-input.correct {
            border-color: #48bb78;
            background-color: #2d5a3d;
        }

        .clue-input.incorrect {
            border-color: #f56565;
            background-color: #5a2d2d;
        }

        .search-area {
            max-width: 600px;
            margin: 0 auto 30px auto;
            padding: 0 20px;
        }

        .search-box {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .search-input {
            flex: 1;
            padding: 12px 16px;
            font-size: 1.1em;
            border: 2px solid #ddd;
            border-radius: 25px;
            transition: all 0.3s;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .clear-btn {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s;
        }

        .clear-btn:hover {
            background: #545b62;
        }

        .search-stats {
            text-align: center;
            color: #666;
            font-size: 0.95em;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .word-group.highlighted {
            border: 2px solid #007bff;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }

        .word-item.highlighted {
            background: #fff3cd;
            border-left-color: #ffc107;
        }

        .highlight-text {
            background: #ffeb3b;
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }

        .test-settings {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .settings-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
            flex-wrap: wrap;
        }

        .mode-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .range-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .range-label {
            font-weight: 500;
            color: #495057;
            font-size: 0.95em;
        }

        .range-input {
            width: 60px;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            text-align: center;
            transition: border-color 0.3s;
        }

        .range-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85em;
        }





        .export-import {
            display: flex;
            gap: 10px;
        }

        .export-import .btn {
            padding: 8px 16px;
            font-size: 0.9em;
        }

        .mode-option {
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            font-size: 0.9em;
            color: #495057;
        }

        .mode-option input[type="radio"] {
            margin: 0;
        }

        .error-info {
            font-size: 0.85em;
            color: #dc3545;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .settings-row {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .mode-section, .range-section {
                justify-content: center;
            }
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast.success {
            background-color: #28a745;
        }

        .toast.error {
            background-color: #dc3545;
        }

        .progress-display {
            background: #3a3a3a;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #555555;
        }

        .progress-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
            gap: 20px;
        }

        .progress-item {
            text-align: center;
            flex: 1;
        }

        .progress-label {
            display: block;
            font-size: 0.9em;
            color: #a0a0a0;
            margin-bottom: 5px;
        }

        .progress-value {
            display: block;
            font-size: 1.8em;
            font-weight: bold;
            color: #e0e0e0;
        }

        .progress-value.correct {
            color: #48bb78;
        }

        .progress-value.remaining {
            color: #4a5568;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #555555;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        @media (max-width: 768px) {
            .progress-stats {
                flex-direction: column;
                gap: 10px;
            }

            .progress-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .progress-label {
                margin-bottom: 0;
            }

            .progress-value {
                font-size: 1.5em;
            }
        }

        .input-area {
            margin: 30px 0;
        }

        .answer-input {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            font-size: 1.5em;
            border: 3px solid #ddd;
            border-radius: 10px;
            text-align: center;
            transition: border-color 0.3s;
        }

        .answer-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            font-size: 1.1em;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }

        .btn-primary {
            background: #4a5568;
            color: #e0e0e0;
        }

        .btn-primary:hover {
            background: #2d3748;
        }

        .btn-secondary {
            background: #718096;
            color: #e0e0e0;
        }

        .btn-secondary:hover {
            background: #4a5568;
        }

        .btn-success {
            background: #48bb78;
            color: #e0e0e0;
        }

        .feedback {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1em;
        }

        .feedback.correct {
            background: #2d5a3d;
            color: #9ae6b4;
            border: 1px solid #48bb78;
        }

        .feedback.incorrect {
            background: #5a2d2d;
            color: #feb2b2;
            border: 1px solid #f56565;
        }

        .word-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .word-group h3 {
            color: #007bff;
            margin-bottom: 15px;
        }

        .word-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .word-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }

        @media (max-width: 600px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .mode-selector {
                justify-content: center;
            }

            .stats {
                justify-content: center;
            }

            .buttons {
                flex-direction: column;
                align-items: center;
            }

            .word-display {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <!-- 艾宾浩斯学习计划表首页 -->
    <div id="ebbinghaus-homepage" class="ebbinghaus-container">
        <h1>🎯 初中英语词性转换艾宾浩斯学习计划表</h1>

        <div class="container">
            <div class="header">
                <h2>设置学习计划</h2>
                <p>将下方的学习单元拖拽到计划表的"学习内容"栏中进行规划。每组包含15个词性转换系列，点击可直接进入对应练习！</p>
            </div>

            <div id="study-plan-setup-area">
                <div id="draggable-numbers-title">可拖拽的学习单元 (第1-9组，每组15个系列) - 点击可直接练习:</div>
                <div id="draggable-numbers-container"></div>

                <div id="study-plan-table-container">
                    <h3>艾宾浩斯遗忘曲线复习计划表</h3>
                    <table id="study-plan-table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">折叠</th>
                                <th style="width: 60px;">序号</th>
                                <th style="width: 100px;">学习日期</th>
                                <th style="width: 150px;">学习内容 (拖入单元)</th>
                                <th style="width: 100px;">第1天</th>
                                <th style="width: 100px;">第2天</th>
                                <th style="width: 100px;">第3天</th>
                                <th style="width: 100px;">第5天</th>
                                <th style="width: 100px;">第8天</th>
                                <th style="width: 100px;">第16天</th>
                                <th style="width: 100px;">第31天</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>

                <div class="plan-row-buttons">
                    <button id="add-plan-row-button">添加新学习日</button>
                    <button id="remove-plan-row-button">删减学习日</button>
                </div>

                <div class="plan-action-buttons-container">
                    <button id="print-button">🖨️ 打印学习表</button>
                    <button id="save-plan-button">💾 保存学习计划</button>
                    <button id="reset-plan-button">🗑️ 重置学习计划</button>
                    <button id="goto-practice-button">📚 进入词性转换练习</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 练习页面 -->
    <div id="practice-page" style="display: none;">
        <div class="container">
        <div class="header">
            <h1>🎯 初中英语词性转换练习</h1>
            <p>掌握词性转换，提升英语水平</p>
        </div>

        <div class="controls">
            <div class="mode-selector">
                <button class="mode-btn active" data-mode="test">测试模式</button>
                <button class="mode-btn" data-mode="review">复习模式</button>
            </div>
            <div class="export-import">
                <button class="btn btn-secondary" id="export-btn">📤 导出结果</button>
                <button class="btn btn-secondary" id="import-btn">📥 导入结果</button>
                <input type="file" id="import-file" accept=".json" style="display: none;">
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>

        <div class="exercise-area">
            <div id="test-mode" class="mode-content">
                <div class="test-settings">
                    <div class="settings-row">
                        <div class="mode-section">
                            <label class="mode-option">
                                <input type="radio" name="test-mode" value="normal" checked>
                                正常模式
                            </label>
                            <label class="mode-option">
                                <input type="radio" name="test-mode" value="errors">
                                错误练习
                            </label>
                            <div class="error-info" id="error-info" style="display: none;">
                                (<span id="error-count">0</span>个错误系列)
                            </div>
                        </div>

                        <div class="range-section" id="range-selector">
                            <span class="range-label">范围:</span>
                            <input type="number" id="range-start" min="1" max="133" value="1" class="range-input">
                            <span>-</span>
                            <input type="number" id="range-end" min="1" max="133" value="133" class="range-input">
                            <button class="btn btn-primary btn-sm" id="apply-range-btn">应用</button>
                        </div>
                    </div>
                </div>

                <div class="progress-display">
                    <div class="progress-stats">
                        <div class="progress-item">
                            <span class="progress-label">总计:</span>
                            <span class="progress-value" id="total-questions">0</span>
                        </div>
                        <div class="progress-item">
                            <span class="progress-label">正确:</span>
                            <span class="progress-value correct" id="correct-questions">0</span>
                        </div>
                        <div class="progress-item">
                            <span class="progress-label">剩余:</span>
                            <span class="progress-value remaining" id="remaining-questions">0</span>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="test-progress-fill"></div>
                    </div>
                </div>

                <div class="question">
                    <h2>根据系列名和词性释义，写出对应的所有单词：</h2>
                    <div class="series-display">
                        <div class="series-title" id="series-title">1. ability系列</div>
                        <div class="series-hint">请写出该系列中所有单词</div>
                    </div>
                </div>

                <div class="clues-area" id="clues-area">
                    <div class="clue-item">
                        <span class="pos-tag">n.</span>
                        <span class="meaning">能力</span>
                        <input type="text" class="clue-input" placeholder="请输入单词..." data-answer="ability">
                    </div>
                    <div class="clue-item">
                        <span class="pos-tag">adj.</span>
                        <span class="meaning">能够的</span>
                        <input type="text" class="clue-input" placeholder="请输入单词..." data-answer="able">
                    </div>
                </div>

                <div class="buttons">
                    <button class="btn btn-primary" id="check-btn">检查答案</button>
                    <button class="btn btn-secondary" id="skip-btn">跳过</button>
                    <button class="btn btn-success" id="next-btn" style="display: none;">下一题</button>
                    <button class="btn btn-secondary" id="back-to-plan-button">🏠 返回学习计划</button>
                </div>

                <div id="feedback" class="feedback" style="display: none;"></div>
            </div>

            <div id="review-mode" class="mode-content" style="display: none;">
                <h2 style="text-align: center; margin-bottom: 30px;">📚 词汇复习</h2>

                <div class="search-area">
                    <div class="search-box">
                        <input type="text" id="search-input" placeholder="搜索单词或系列名..." class="search-input">
                        <button id="clear-search" class="clear-btn" style="display: none;">清除</button>
                    </div>
                    <div class="search-stats" id="search-stats" style="display: none;"></div>
                </div>

                <div id="word-groups"></div>
            </div>
        </div>
    </div>

    <script>
        // 词汇数据 - 直接定义完整的词汇组
        function getWordData() {
            return [
                {
                    group: "1. ability系列",
                    words: [
                        { word: "ability", pos: "n.", meaning: "能力" },
                        { word: "able", pos: "adj.", meaning: "能够的" },
                        { word: "enable", pos: "v.", meaning: "使能够" },
                        { word: "disable", pos: "v.", meaning: "使残疾" },
                        { word: "disabled", pos: "adj.", meaning: "残疾的" },
                        { word: "unable", pos: "adj.", meaning: "不能的" }
                    ]
                },
                {
                    group: "2. accept系列",
                    words: [
                        { word: "accept", pos: "v.", meaning: "接受" },
                        { word: "acceptable", pos: "adj.", meaning: "可接受的" }
                    ]
                },
                {
                    group: "3. accident系列",
                    words: [
                        { word: "accident", pos: "n.", meaning: "事故" },
                        { word: "accidental", pos: "adj.", meaning: "意外的" },
                        { word: "accidentally", pos: "adv.", meaning: "意外地" }
                    ]
                },
                {
                    group: "4. accurate系列",
                    words: [
                        { word: "accurate", pos: "adj.", meaning: "准确的" },
                        { word: "accuracy", pos: "n.", meaning: "准确性" },
                        { word: "accurately", pos: "adv.", meaning: "准确地" }
                    ]
                },
                {
                    group: "5. achieve系列",
                    words: [
                        { word: "achieve", pos: "v.", meaning: "实现" },
                        { word: "achievement", pos: "n.", meaning: "成就" }
                    ]
                },
                {
                    group: "6. cross系列",
                    words: [
                        { word: "across", pos: "prep. & adv.", meaning: "穿过" },
                        { word: "cross", pos: "v. & n.", meaning: "穿过/十字" }
                    ]
                },
                {
                    group: "7. act系列",
                    words: [
                        { word: "act", pos: "v. & n.", meaning: "行动" },
                        { word: "action", pos: "n.", meaning: "行动" },
                        { word: "active", pos: "adj.", meaning: "积极的" },
                        { word: "activity", pos: "n.", meaning: "活动" },
                        { word: "actor", pos: "n.", meaning: "演员" },
                        { word: "actress", pos: "n.", meaning: "女演员" }
                    ]
                },
                {
                    group: "8. actual系列",
                    words: [
                        { word: "actually", pos: "adv.", meaning: "实际上" },
                        { word: "actual", pos: "adj.", meaning: "实际的" }
                    ]
                },
                {
                    group: "9. add系列",
                    words: [
                        { word: "add", pos: "v.", meaning: "添加" },
                        { word: "addition", pos: "n.", meaning: "添加" }
                    ]
                },
                {
                    group: "10. advertise系列",
                    words: [
                        { word: "advertisement", pos: "n.", meaning: "广告" },
                        { word: "advertise", pos: "v.", meaning: "做广告" }
                    ]
                },
                {
                    group: "11. advice系列",
                    words: [
                        { word: "advice", pos: "n.", meaning: "建议" },
                        { word: "advise", pos: "v.", meaning: "建议" }
                    ]
                },
                {
                    group: "12. Africa系列",
                    words: [
                        { word: "Africa", pos: "n.", meaning: "非洲" },
                        { word: "African", pos: "adj.", meaning: "非洲的" }
                    ]
                },
                {
                    group: "13. age系列",
                    words: [
                        { word: "age", pos: "n.", meaning: "年龄" },
                        { word: "aged", pos: "adj.", meaning: "年老的" }
                    ]
                },
                {
                    group: "14. agree系列",
                    words: [
                        { word: "agree", pos: "v.", meaning: "同意" },
                        { word: "agreement", pos: "n.", meaning: "协议" },
                        { word: "disagree", pos: "v.", meaning: "不同意" }
                    ]
                },
                {
                    group: "15. live系列",
                    words: [
                        { word: "alive", pos: "adj.", meaning: "活着的" },
                        { word: "live", pos: "v.", meaning: "生活" },
                        { word: "living", pos: "adj.", meaning: "活的" },
                        { word: "life", pos: "n.", meaning: "生活" }
                    ]
                },
                {
                    group: "16. amaze系列",
                    words: [
                        { word: "amazing", pos: "adj.", meaning: "令人惊奇的" },
                        { word: "amaze", pos: "v.", meaning: "使惊奇" }
                    ]
                },
                {
                    group: "17. ambition系列",
                    words: [
                        { word: "ambition", pos: "n.", meaning: "雄心" },
                        { word: "ambitious", pos: "adj.", meaning: "有雄心的" }
                    ]
                },
                {
                    group: "18. America系列",
                    words: [
                        { word: "America", pos: "n.", meaning: "美国" },
                        { word: "American", pos: "adj. & n.", meaning: "美国的/美国人" }
                    ]
                },
                {
                    group: "19. amuse系列",
                    words: [
                        { word: "amusement", pos: "n.", meaning: "娱乐" },
                        { word: "amuse", pos: "v.", meaning: "娱乐" },
                        { word: "amusing", pos: "adj.", meaning: "有趣的" }
                    ]
                },
                {
                    group: "20. angry系列",
                    words: [
                        { word: "angrily", pos: "adv.", meaning: "愤怒地" },
                        { word: "angry", pos: "adj.", meaning: "愤怒的" }
                    ]
                },
                {
                    group: "21. apologize系列",
                    words: [
                        { word: "apologize", pos: "v.", meaning: "道歉" },
                        { word: "apology", pos: "n.", meaning: "道歉" }
                    ]
                },
                {
                    group: "22. appear系列",
                    words: [
                        { word: "appear", pos: "v.", meaning: "出现" },
                        { word: "appearance", pos: "n.", meaning: "外观" },
                        { word: "disappear", pos: "v.", meaning: "消失" }
                    ]
                },
                {
                    group: "23. apply系列",
                    words: [
                        { word: "apply", pos: "v.", meaning: "申请" },
                        { word: "application", pos: "n.", meaning: "申请" }
                    ]
                },
                {
                    group: "24. argue系列",
                    words: [
                        { word: "argue", pos: "v.", meaning: "争论" },
                        { word: "argument", pos: "n.", meaning: "争论" }
                    ]
                },
                {
                    group: "25. arrange系列",
                    words: [
                        { word: "arrange", pos: "v.", meaning: "安排" },
                        { word: "arrangement", pos: "n.", meaning: "安排" }
                    ]
                },
                {
                    group: "26. arrive系列",
                    words: [
                        { word: "arrive", pos: "v.", meaning: "到达" },
                        { word: "arrival", pos: "n.", meaning: "到达" }
                    ]
                },
                {
                    group: "27. art系列",
                    words: [
                        { word: "art", pos: "n.", meaning: "艺术" },
                        { word: "artist", pos: "n.", meaning: "艺术家" },
                        { word: "artistic", pos: "adj.", meaning: "艺术的" }
                    ]
                },
                {
                    group: "28. Asia系列",
                    words: [
                        { word: "Asia", pos: "n.", meaning: "亚洲" },
                        { word: "Asian", pos: "adj.", meaning: "亚洲的" }
                    ]
                },
                {
                    group: "29. attract系列",
                    words: [
                        { word: "attract", pos: "v.", meaning: "吸引" },
                        { word: "attraction", pos: "n.", meaning: "吸引力" },
                        { word: "attractive", pos: "adj.", meaning: "有吸引力的" },
                        { word: "attractively", pos: "adv.", meaning: "有吸引力地" }
                    ]
                },
                {
                    group: "30. Australia系列",
                    words: [
                        { word: "Australia", pos: "n.", meaning: "澳大利亚" },
                        { word: "Australian", pos: "adj. & n.", meaning: "澳大利亚的/澳大利亚人" }
                    ]
                },
                {
                    group: "31. automatic系列",
                    words: [
                        { word: "automatic", pos: "adj.", meaning: "自动的" },
                        { word: "automatically", pos: "adv.", meaning: "自动地" }
                    ]
                },
                {
                    group: "32. available系列",
                    words: [
                        { word: "available", pos: "adj.", meaning: "可用的" },
                        { word: "availability", pos: "n.", meaning: "可用性" }
                    ]
                },
                {
                    group: "33. average系列",
                    words: [
                        { word: "average", pos: "adj. & n.", meaning: "平均的/平均数" },
                        { word: "averagely", pos: "adv.", meaning: "平均地" }
                    ]
                },
                {
                    group: "34. awful系列",
                    words: [
                        { word: "awful", pos: "adj.", meaning: "可怕的" },
                        { word: "awfully", pos: "adv.", meaning: "可怕地" }
                    ]
                },
                {
                    group: "35. bad系列",
                    words: [
                        { word: "bad", pos: "adj.", meaning: "坏的" },
                        { word: "badly", pos: "adv.", meaning: "坏地" }
                    ]
                },
                {
                    group: "36. bake系列",
                    words: [
                        { word: "bakery", pos: "n.", meaning: "面包店" },
                        { word: "bake", pos: "v.", meaning: "烘烤" },
                        { word: "baker", pos: "n.", meaning: "面包师" }
                    ]
                },
                {
                    group: "37. balance系列",
                    words: [
                        { word: "balance", pos: "n.", meaning: "平衡" },
                        { word: "balanced", pos: "adj.", meaning: "平衡的" }
                    ]
                },
                {
                    group: "38. basic系列",
                    words: [
                        { word: "basic", pos: "adj.", meaning: "基本的" },
                        { word: "base", pos: "n.", meaning: "基础" },
                        { word: "basically", pos: "adv.", meaning: "基本上" },
                        { word: "basics", pos: "n.", meaning: "基础知识" }
                    ]
                },
                {
                    group: "39. bath系列",
                    words: [
                        { word: "bath", pos: "n.", meaning: "洗澡" },
                        { word: "bathe", pos: "v.", meaning: "洗澡" }
                    ]
                },
                {
                    group: "40. beautiful系列",
                    words: [
                        { word: "beautiful", pos: "adj.", meaning: "美丽的" },
                        { word: "beautifully", pos: "adv.", meaning: "美丽地" },
                        { word: "beauty", pos: "n.", meaning: "美丽" }
                    ]
                },
                {
                    group: "41. beg系列",
                    words: [
                        { word: "beg", pos: "v.", meaning: "乞求" },
                        { word: "beggar", pos: "n.", meaning: "乞丐" }
                    ]
                },
                {
                    group: "42. begin系列",
                    words: [
                        { word: "begin", pos: "v.", meaning: "开始" },
                        { word: "beginning", pos: "n.", meaning: "开始" }
                    ]
                },
                {
                    group: "43. behave系列",
                    words: [
                        { word: "behaviour", pos: "n.", meaning: "行为" },
                        { word: "behave", pos: "v.", meaning: "表现" }
                    ]
                },
                {
                    group: "44. believe系列",
                    words: [
                        { word: "believe", pos: "v.", meaning: "相信" },
                        { word: "believable", pos: "adj.", meaning: "可信的" },
                        { word: "unbelievable", pos: "adj.", meaning: "难以置信的" },
                        { word: "believably", pos: "adv.", meaning: "可信地" },
                        { word: "belief", pos: "n.", meaning: "信念" }
                    ]
                },
                {
                    group: "45. board系列",
                    words: [
                        { word: "blackboard", pos: "n.", meaning: "黑板" },
                        { word: "board", pos: "n. & v.", meaning: "板子/上船" }
                    ]
                },
                {
                    group: "46. boil系列",
                    words: [
                        { word: "boil", pos: "v.", meaning: "煮沸" },
                        { word: "boiled", pos: "adj.", meaning: "煮熟的" },
                        { word: "boiling", pos: "adj.", meaning: "沸腾的" }
                    ]
                },
                {
                    group: "47. bore系列",
                    words: [
                        { word: "bored", pos: "adj.", meaning: "无聊的" },
                        { word: "boring", pos: "adj.", meaning: "令人无聊的" }
                    ]
                },
                {
                    group: "48. brave系列",
                    words: [
                        { word: "brave", pos: "adj.", meaning: "勇敢的" },
                        { word: "bravely", pos: "adv.", meaning: "勇敢地" },
                        { word: "bravery", pos: "n.", meaning: "勇敢" }
                    ]
                },
                {
                    group: "49. breath系列",
                    words: [
                        { word: "breath", pos: "n.", meaning: "呼吸" },
                        { word: "breathe", pos: "v.", meaning: "呼吸" }
                    ]
                },
                {
                    group: "50. bright系列",
                    words: [
                        { word: "bright", pos: "adj.", meaning: "明亮的" },
                        { word: "brightly", pos: "adv.", meaning: "明亮地" }
                    ]
                },
                {
                    group: "51. Britain系列",
                    words: [
                        { word: "Britain", pos: "n.", meaning: "英国" },
                        { word: "British", pos: "adj. & n.", meaning: "英国的/英国人" },
                        { word: "Briton", pos: "n.", meaning: "英国人" }
                    ]
                },
                {
                    group: "52. build系列",
                    words: [
                        { word: "builder", pos: "n.", meaning: "建筑工人" },
                        { word: "building", pos: "n.", meaning: "建筑物" },
                        { word: "rebuild", pos: "v.", meaning: "重建" }
                    ]
                },
                {
                    group: "53. business系列",
                    words: [
                        { word: "business", pos: "n.", meaning: "商业" },
                        { word: "businessman", pos: "n.", meaning: "商人" },
                        { word: "businesswoman", pos: "n.", meaning: "女商人" },
                        { word: "busy", pos: "adj.", meaning: "忙碌的" }
                    ]
                },
                {
                    group: "54. calm系列",
                    words: [
                        { word: "calm", pos: "adj.", meaning: "平静的" },
                        { word: "calmly", pos: "adv.", meaning: "平静地" }
                    ]
                },
                {
                    group: "55. Canada系列",
                    words: [
                        { word: "Canada", pos: "n.", meaning: "加拿大" },
                        { word: "Canadian", pos: "adj. & n.", meaning: "加拿大的/加拿大人" }
                    ]
                },
                {
                    group: "56. care系列",
                    words: [
                        { word: "care", pos: "n. & v.", meaning: "关心" },
                        { word: "careful", pos: "adj.", meaning: "小心的" },
                        { word: "carefully", pos: "adv.", meaning: "小心地" },
                        { word: "careless", pos: "adj.", meaning: "粗心的" }
                    ]
                },
                {
                    group: "57. celebrate系列",
                    words: [
                        { word: "celebrate", pos: "v.", meaning: "庆祝" },
                        { word: "celebration", pos: "n.", meaning: "庆祝" },
                        { word: "celebratory", pos: "adj.", meaning: "庆祝的" }
                    ]
                },
                {
                    group: "58. centre系列",
                    words: [
                        { word: "centre", pos: "n.", meaning: "中心" },
                        { word: "central", pos: "adj.", meaning: "中央的" }
                    ]
                },
                {
                    group: "59. certain系列",
                    words: [
                        { word: "certainly", pos: "adv.", meaning: "当然" },
                        { word: "certain", pos: "adj.", meaning: "确定的" }
                    ]
                },
                {
                    group: "60. change系列",
                    words: [
                        { word: "change", pos: "n.", meaning: "变化" },
                        { word: "changeable", pos: "adj.", meaning: "可变的" },
                        { word: "changer", pos: "n.", meaning: "改变者" },
                        { word: "exchange", pos: "v.", meaning: "交换" }
                    ]
                },
                {
                    group: "61. chemical系列",
                    words: [
                        { word: "chemical", pos: "n. & adj.", meaning: "化学品/化学的" },
                        { word: "chemist", pos: "n.", meaning: "化学家" },
                        { word: "chemistry", pos: "n.", meaning: "化学" }
                    ]
                },
                {
                    group: "62. China系列",
                    words: [
                        { word: "China", pos: "n.", meaning: "中国" },
                        { word: "Chinese", pos: "n. & adj.", meaning: "中国人/中国的" }
                    ]
                },
                {
                    group: "63. choose系列",
                    words: [
                        { word: "choice", pos: "n.", meaning: "选择" },
                        { word: "choose", pos: "v.", meaning: "选择" }
                    ]
                },
                {
                    group: "64. city系列",
                    words: [
                        { word: "citizen", pos: "n.", meaning: "公民" },
                        { word: "city", pos: "n.", meaning: "城市" }
                    ]
                },
                {
                    group: "65. clear系列",
                    words: [
                        { word: "clear", pos: "adj.", meaning: "清楚的" },
                        { word: "clearly", pos: "adv.", meaning: "清楚地" }
                    ]
                },
                {
                    group: "66. weather系列",
                    words: [
                        { word: "cloud", pos: "n.", meaning: "云" },
                        { word: "cloudy", pos: "adj.", meaning: "多云的" },
                        { word: "fog", pos: "n.", meaning: "雾" },
                        { word: "foggy", pos: "adj.", meaning: "有雾的" },
                        { word: "rain", pos: "n.", meaning: "雨" },
                        { word: "rainy", pos: "adj.", meaning: "下雨的" },
                        { word: "snow", pos: "n.", meaning: "雪" },
                        { word: "snowy", pos: "adj.", meaning: "下雪的" },
                        { word: "sun", pos: "n.", meaning: "太阳" },
                        { word: "sunny", pos: "adj.", meaning: "晴朗的" },
                        { word: "wind", pos: "n.", meaning: "风" },
                        { word: "windy", pos: "adj.", meaning: "有风的" }
                    ]
                },
                {
                    group: "67. collect系列",
                    words: [
                        { word: "collect", pos: "v.", meaning: "收集" },
                        { word: "collection", pos: "n.", meaning: "收集" }
                    ]
                },
                {
                    group: "68. colour系列",
                    words: [
                        { word: "colour", pos: "n.", meaning: "颜色" },
                        { word: "colourful", pos: "adj.", meaning: "多彩的" },
                        { word: "colourless", pos: "adj.", meaning: "无色的" }
                    ]
                },
                {
                    group: "69. comfort系列",
                    words: [
                        { word: "comfortable", pos: "adj.", meaning: "舒适的" },
                        { word: "comfort", pos: "v.", meaning: "安慰" },
                        { word: "comfortably", pos: "adv.", meaning: "舒适地" }
                    ]
                },
                {
                    group: "70. communicate系列",
                    words: [
                        { word: "communicate", pos: "v.", meaning: "交流" },
                        { word: "communication", pos: "n.", meaning: "交流" },
                        { word: "communicative", pos: "adj.", meaning: "善于交流的" },
                        { word: "communicatively", pos: "adv.", meaning: "善于交流地" }
                    ]
                },
                {
                    group: "71. complain系列",
                    words: [
                        { word: "complain", pos: "v.", meaning: "抱怨" },
                        { word: "complaint", pos: "n.", meaning: "抱怨" }
                    ]
                },
                {
                    group: "72. complete系列",
                    words: [
                        { word: "complete", pos: "adj.", meaning: "完整的" },
                        { word: "completely", pos: "adv.", meaning: "完全地" }
                    ]
                },
                {
                    group: "73. conclude系列",
                    words: [
                        { word: "conclusion", pos: "n.", meaning: "结论" },
                        { word: "conclude", pos: "v.", meaning: "得出结论" }
                    ]
                },
                {
                    group: "74. confidence系列",
                    words: [
                        { word: "confidence", pos: "n.", meaning: "信心" },
                        { word: "confident", pos: "adj.", meaning: "自信的" }
                    ]
                },
                {
                    group: "75. confuse系列",
                    words: [
                        { word: "confuse", pos: "v.", meaning: "使困惑" },
                        { word: "confused", pos: "adj.", meaning: "困惑的" },
                        { word: "confusing", pos: "adj.", meaning: "令人困惑的" },
                        { word: "confusion", pos: "n.", meaning: "困惑" }
                    ]
                },
                {
                    group: "76. congratulate系列",
                    words: [
                        { word: "congratulation", pos: "n.", meaning: "祝贺" },
                        { word: "congratulate", pos: "v.", meaning: "祝贺" }
                    ]
                },
                {
                    group: "77. connect系列",
                    words: [
                        { word: "connect", pos: "v.", meaning: "连接" },
                        { word: "connection", pos: "n.", meaning: "连接" }
                    ]
                },
                {
                    group: "78. consider系列",
                    words: [
                        { word: "consider", pos: "v.", meaning: "考虑" },
                        { word: "considerate", pos: "adj.", meaning: "体贴的" },
                        { word: "consideration", pos: "n.", meaning: "考虑" }
                    ]
                },
                {
                    group: "79. continue系列",
                    words: [
                        { word: "continue", pos: "v.", meaning: "继续" },
                        { word: "continuous", pos: "adj.", meaning: "连续的" }
                    ]
                },
                {
                    group: "80. convenient系列",
                    words: [
                        { word: "convenient", pos: "adj.", meaning: "方便的" },
                        { word: "convenience", pos: "n.", meaning: "方便" }
                    ]
                },
                {
                    group: "81. cook系列",
                    words: [
                        { word: "cook", pos: "n.", meaning: "厨师" },
                        { word: "cooked", pos: "adj.", meaning: "煮熟的" },
                        { word: "cooker", pos: "n.", meaning: "炊具" },
                        { word: "cookery", pos: "n.", meaning: "烹饪" }
                    ]
                },
                {
                    group: "82. correct系列",
                    words: [
                        { word: "correct", pos: "adj.", meaning: "正确的" },
                        { word: "correction", pos: "n.", meaning: "纠正" },
                        { word: "correctly", pos: "adv.", meaning: "正确地" },
                        { word: "incorrect", pos: "adj.", meaning: "不正确的" },
                        { word: "uncorrected", pos: "adj.", meaning: "未纠正的" }
                    ]
                },
                {
                    group: "83. count系列",
                    words: [
                        { word: "count", pos: "v.", meaning: "数" },
                        { word: "countless", pos: "adj.", meaning: "无数的" }
                    ]
                },
                {
                    group: "84. cover系列",
                    words: [
                        { word: "cover", pos: "v.", meaning: "覆盖" },
                        { word: "coverage", pos: "n.", meaning: "覆盖" }
                    ]
                },
                {
                    group: "85. create系列",
                    words: [
                        { word: "create", pos: "v.", meaning: "创造" },
                        { word: "creative", pos: "adj.", meaning: "有创造力的" }
                    ]
                },
                {
                    group: "86. crowd系列",
                    words: [
                        { word: "crowd", pos: "n.", meaning: "人群" },
                        { word: "crowded", pos: "adj.", meaning: "拥挤的" }
                    ]
                },
                {
                    group: "87. culture系列",
                    words: [
                        { word: "culture", pos: "n.", meaning: "文化" },
                        { word: "cultural", pos: "adj.", meaning: "文化的" }
                    ]
                },
                {
                    group: "88. custom系列",
                    words: [
                        { word: "custom", pos: "n.", meaning: "习俗" },
                        { word: "customer", pos: "n.", meaning: "顾客" },
                        { word: "customs", pos: "n.", meaning: "海关" }
                    ]
                },
                {
                    group: "89. cycle系列",
                    words: [
                        { word: "cycle", pos: "v.", meaning: "骑自行车" },
                        { word: "cycling", pos: "n.", meaning: "骑自行车" },
                        { word: "cyclist", pos: "n.", meaning: "骑自行车的人" },
                        { word: "recycle", pos: "v.", meaning: "回收" }
                    ]
                },
                {
                    group: "90. dance系列",
                    words: [
                        { word: "dance", pos: "n. & v.", meaning: "舞蹈/跳舞" },
                        { word: "dancer", pos: "n.", meaning: "舞者" }
                    ]
                },
                {
                    group: "91. danger系列",
                    words: [
                        { word: "danger", pos: "n.", meaning: "危险" },
                        { word: "dangerous", pos: "adj.", meaning: "危险的" },
                        { word: "dangerously", pos: "adv.", meaning: "危险地" }
                    ]
                },
                {
                    group: "92. dark系列",
                    words: [
                        { word: "dark", pos: "adj.", meaning: "黑暗的" },
                        { word: "darkness", pos: "n.", meaning: "黑暗" }
                    ]
                },
                {
                    group: "93. death系列",
                    words: [
                        { word: "dead", pos: "adj.", meaning: "死的" },
                        { word: "death", pos: "n.", meaning: "死亡" },
                        { word: "die", pos: "v.", meaning: "死" },
                        { word: "dying", pos: "adj.", meaning: "垂死的" }
                    ]
                },
                {
                    group: "94. decide系列",
                    words: [
                        { word: "decide", pos: "v.", meaning: "决定" },
                        { word: "decision", pos: "n.", meaning: "决定" }
                    ]
                },
                {
                    group: "95. decorate系列",
                    words: [
                        { word: "decorate", pos: "v.", meaning: "装饰" },
                        { word: "decoration", pos: "n.", meaning: "装饰" }
                    ]
                },
                {
                    group: "96. deep系列",
                    words: [
                        { word: "deep", pos: "adj.", meaning: "深的" },
                        { word: "depth", pos: "n.", meaning: "深度" }
                    ]
                },
                {
                    group: "97. depart系列",
                    words: [
                        { word: "departure", pos: "n.", meaning: "出发" },
                        { word: "depart", pos: "v.", meaning: "出发" }
                    ]
                },
                {
                    group: "98. depend系列",
                    words: [
                        { word: "depend", pos: "v.", meaning: "依靠" },
                        { word: "dependence", pos: "n.", meaning: "依赖" },
                        { word: "dependent", pos: "adj.", meaning: "依赖的" },
                        { word: "independent", pos: "adj.", meaning: "独立的" }
                    ]
                },
                {
                    group: "99. describe系列",
                    words: [
                        { word: "describe", pos: "v.", meaning: "描述" },
                        { word: "description", pos: "n.", meaning: "描述" }
                    ]
                },
                {
                    group: "100. design系列",
                    words: [
                        { word: "design", pos: "v.", meaning: "设计" },
                        { word: "designer", pos: "n.", meaning: "设计师" }
                    ]
                },
                {
                    group: "101. desire系列",
                    words: [
                        { word: "desire", pos: "n.", meaning: "欲望" },
                        { word: "desirable", pos: "adj.", meaning: "令人向往的" }
                    ]
                },
                {
                    group: "102. develop系列",
                    words: [
                        { word: "develop", pos: "v.", meaning: "发展" },
                        { word: "developed", pos: "adj.", meaning: "发达的" },
                        { word: "developing", pos: "adj.", meaning: "发展中的" },
                        { word: "development", pos: "n.", meaning: "发展" }
                    ]
                },
                {
                    group: "103. different系列",
                    words: [
                        { word: "difference", pos: "n.", meaning: "差异" },
                        { word: "different", pos: "adj.", meaning: "不同的" }
                    ]
                },
                {
                    group: "104. difficult系列",
                    words: [
                        { word: "difficult", pos: "adj.", meaning: "困难的" },
                        { word: "difficulty", pos: "n.", meaning: "困难" }
                    ]
                },
                {
                    group: "105. direct系列",
                    words: [
                        { word: "direction", pos: "n.", meaning: "方向" },
                        { word: "direct", pos: "adj.", meaning: "直接的" },
                        { word: "directly", pos: "adv.", meaning: "直接地" },
                        { word: "director", pos: "n.", meaning: "导演" }
                    ]
                },
                {
                    group: "106. disappoint系列",
                    words: [
                        { word: "disappointed", pos: "adj.", meaning: "失望的" },
                        { word: "disappoint", pos: "v.", meaning: "使失望" },
                        { word: "disappointing", pos: "adj.", meaning: "令人失望的" },
                        { word: "disappointment", pos: "n.", meaning: "失望" }
                    ]
                },
                {
                    group: "107. discover系列",
                    words: [
                        { word: "discover", pos: "v.", meaning: "发现" },
                        { word: "discovery", pos: "n.", meaning: "发现" }
                    ]
                },
                {
                    group: "108. discuss系列",
                    words: [
                        { word: "discuss", pos: "v.", meaning: "讨论" },
                        { word: "discussion", pos: "n.", meaning: "讨论" }
                    ]
                },
                {
                    group: "109. draw系列",
                    words: [
                        { word: "draw", pos: "v.", meaning: "画" },
                        { word: "drawing", pos: "n.", meaning: "绘画" }
                    ]
                },
                {
                    group: "110. drive系列",
                    words: [
                        { word: "drive", pos: "v.", meaning: "驾驶" },
                        { word: "driver", pos: "n.", meaning: "司机" }
                    ]
                },
                {
                    group: "111. easy系列",
                    words: [
                        { word: "easily", pos: "adv.", meaning: "容易地" },
                        { word: "easy", pos: "adj.", meaning: "容易的" }
                    ]
                },
                {
                    group: "112. education系列",
                    words: [
                        { word: "education", pos: "n.", meaning: "教育" },
                        { word: "educate", pos: "v.", meaning: "教育" },
                        { word: "educational", pos: "adj.", meaning: "教育的" },
                        { word: "educator", pos: "n.", meaning: "教育者" }
                    ]
                },
                {
                    group: "113. effect系列",
                    words: [
                        { word: "effect", pos: "n.", meaning: "效果" },
                        { word: "effective", pos: "adj.", meaning: "有效的" }
                    ]
                },
                {
                    group: "114. elder系列",
                    words: [
                        { word: "elder", pos: "adj.", meaning: "年长的" },
                        { word: "elderly", pos: "adj.", meaning: "年老的" }
                    ]
                },
                {
                    group: "115. electric系列",
                    words: [
                        { word: "electric", pos: "adj.", meaning: "电的" },
                        { word: "electrical", pos: "adj.", meaning: "电气的" },
                        { word: "electricity", pos: "n.", meaning: "电" },
                        { word: "electronic", pos: "adj.", meaning: "电子的" }
                    ]
                },
                {
                    group: "116. embarrass系列",
                    words: [
                        { word: "embarrassed", pos: "adj.", meaning: "尴尬的" },
                        { word: "embarrass", pos: "v.", meaning: "使尴尬" },
                        { word: "embarrassment", pos: "n.", meaning: "尴尬" },
                        { word: "embarrassing", pos: "adj.", meaning: "令人尴尬的" }
                    ]
                },
                {
                    group: "117. encourage系列",
                    words: [
                        { word: "encourage", pos: "v.", meaning: "鼓励" },
                        { word: "encouragement", pos: "n.", meaning: "鼓励" }
                    ]
                },
                {
                    group: "118. engine系列",
                    words: [
                        { word: "engine", pos: "n.", meaning: "引擎" },
                        { word: "engineer", pos: "n.", meaning: "工程师" }
                    ]
                },
                {
                    group: "119. England系列",
                    words: [
                        { word: "England", pos: "n.", meaning: "英格兰" },
                        { word: "English", pos: "adj. & n.", meaning: "英语的/英语" }
                    ]
                },
                {
                    group: "120. enjoy系列",
                    words: [
                        { word: "enjoy", pos: "v.", meaning: "享受" },
                        { word: "enjoyable", pos: "adj.", meaning: "令人愉快的" },
                        { word: "joy", pos: "n.", meaning: "快乐" }
                    ]
                },
                {
                    group: "121. rich系列",
                    words: [
                        { word: "enrich", pos: "v.", meaning: "丰富" },
                        { word: "rich", pos: "adj.", meaning: "富有的" }
                    ]
                },
                {
                    group: "122. enter系列",
                    words: [
                        { word: "enter", pos: "v.", meaning: "进入" },
                        { word: "entrance", pos: "n.", meaning: "入口" }
                    ]
                },
                {
                    group: "123. entertain系列",
                    words: [
                        { word: "entertainment", pos: "n.", meaning: "娱乐" },
                        { word: "entertain", pos: "v.", meaning: "娱乐" }
                    ]
                },
                {
                    group: "124. environment系列",
                    words: [
                        { word: "environment", pos: "n.", meaning: "环境" },
                        { word: "environmental", pos: "adj.", meaning: "环境的" }
                    ]
                },
                {
                    group: "125. especial系列",
                    words: [
                        { word: "especially", pos: "adv.", meaning: "特别地" },
                        { word: "especial", pos: "adj.", meaning: "特别的" }
                    ]
                },
                {
                    group: "126. Europe系列",
                    words: [
                        { word: "Europe", pos: "n.", meaning: "欧洲" },
                        { word: "European", pos: "adj. & n.", meaning: "欧洲的/欧洲人" }
                    ]
                },
                {
                    group: "127. exact系列",
                    words: [
                        { word: "exactly", pos: "adv.", meaning: "确切地" },
                        { word: "exact", pos: "adj.", meaning: "确切的" }
                    ]
                },
                {
                    group: "128. exam系列",
                    words: [
                        { word: "exam", pos: "n.", meaning: "考试" },
                        { word: "examine", pos: "v.", meaning: "检查" }
                    ]
                },
                {
                    group: "129. excite系列",
                    words: [
                        { word: "excited", pos: "adj.", meaning: "兴奋的" },
                        { word: "excite", pos: "v.", meaning: "使兴奋" },
                        { word: "excitedly", pos: "adv.", meaning: "兴奋地" },
                        { word: "excitement", pos: "n.", meaning: "兴奋" },
                        { word: "exciting", pos: "adj.", meaning: "令人兴奋的" }
                    ]
                },
                {
                    group: "130. expensive系列",
                    words: [
                        { word: "expensive", pos: "adj.", meaning: "昂贵的" },
                        { word: "expense", pos: "n.", meaning: "费用" },
                        { word: "inexpensive", pos: "adj.", meaning: "便宜的" }
                    ]
                },
                {
                    group: "131. experience系列",
                    words: [
                        { word: "experience", pos: "n.", meaning: "经验" },
                        { word: "experienced", pos: "adj.", meaning: "有经验的" }
                    ]
                },
                {
                    group: "132. explain系列",
                    words: [
                        { word: "explain", pos: "v.", meaning: "解释" },
                        { word: "explanation", pos: "n.", meaning: "解释" }
                    ]
                },
                {
                    group: "133. explore系列",
                    words: [
                        { word: "explore", pos: "v.", meaning: "探索" },
                        { word: "explorer", pos: "n.", meaning: "探险家" }
                    ]
                }
            ];
        }

        const wordData = getWordData();

        // 练习状态
        let currentMode = 'test';
        let currentQuestion = 0;
        let correctCount = 0;
        let wrongCount = 0;
        let currentWordGroup = null;
        let currentTargetWord = null;
        let testRangeStart = 1;
        let testRangeEnd = 133;
        let isErrorMode = false;
        let errorRecords = []; // 错误记录
        let testResults = []; // 测试结果记录
        let totalQuestions = 0; // 总题数
        let testedSeries = new Set(); // 已测试的系列
        let currentSessionErrors = []; // 当前会话中的错误组，用于重复练习

        // 艾宾浩斯学习计划相关变量
        let currentPage = 'ebbinghaus'; // 'ebbinghaus' 或 'practice'
        const planTableStorageKey = 'wordTransformationEbbinghausStudyPlan2025_v1';
        let collapseStates = {}; // 存储每个组的折叠状态
        const rowsPerGroup = 10; // 每组10行

        // 词性转换分组定义 (每组15个系列)
        const wordGroups = [
            { number: 1, range: '1-15', description: '第1组: ability-agree系列 (15个系列)' },
            { number: 2, range: '16-30', description: '第2组: live-Australia系列 (15个系列)' },
            { number: 3, range: '31-45', description: '第3组: automatic-board系列 (15个系列)' },
            { number: 4, range: '46-60', description: '第4组: boil-centre系列 (15个系列)' },
            { number: 5, range: '61-75', description: '第5组: certain-confuse系列 (15个系列)' },
            { number: 6, range: '76-90', description: '第6组: congratulate-culture系列 (15个系列)' },
            { number: 7, range: '91-105', description: '第7组: custom-direct系列 (15个系列)' },
            { number: 8, range: '106-120', description: '第8组: disappoint-enjoy系列 (15个系列)' },
            { number: 9, range: '121-133', description: '第9组: rich-explore系列 (13个系列)' }
        ];

        // DOM元素
        const modeButtons = document.querySelectorAll('.mode-btn');
        const modeContents = document.querySelectorAll('.mode-content');
        const seriesTitleEl = document.getElementById('series-title');
        const cluesAreaEl = document.getElementById('clues-area');
        const checkBtn = document.getElementById('check-btn');
        const skipBtn = document.getElementById('skip-btn');
        const nextBtn = document.getElementById('next-btn');
        const feedbackEl = document.getElementById('feedback');

        const progressFill = document.getElementById('progress-fill');
        const searchInput = document.getElementById('search-input');
        const clearSearchBtn = document.getElementById('clear-search');
        const searchStats = document.getElementById('search-stats');
        const rangeStartInput = document.getElementById('range-start');
        const rangeEndInput = document.getElementById('range-end');
        const applyRangeBtn = document.getElementById('apply-range-btn');
        const exportBtn = document.getElementById('export-btn');
        const importBtn = document.getElementById('import-btn');
        const importFile = document.getElementById('import-file');
        const testModeRadios = document.querySelectorAll('input[name="test-mode"]');
        const errorInfo = document.getElementById('error-info');
        const errorCount = document.getElementById('error-count');
        const rangeSelector = document.getElementById('range-selector');
        const totalQuestionsEl = document.getElementById('total-questions');
        const correctQuestionsEl = document.getElementById('correct-questions');
        const remainingQuestionsEl = document.getElementById('remaining-questions');
        const testProgressFill = document.getElementById('test-progress-fill');

        // 艾宾浩斯页面元素
        const ebbinghausHomepage = document.getElementById('ebbinghaus-homepage');
        const practicePage = document.getElementById('practice-page');
        const gotoPracticeButton = document.getElementById('goto-practice-button');
        const backToPlanButton = document.getElementById('back-to-plan-button');
        const savePlanButton = document.getElementById('save-plan-button');
        const resetPlanButton = document.getElementById('reset-plan-button');
        const printButton = document.getElementById('print-button');
        const addPlanRowButton = document.getElementById('add-plan-row-button');
        const removePlanRowButton = document.getElementById('remove-plan-row-button');

        // 初始化
        init();

        function init() {
            setupEventListeners();
            updateErrorInfo();
            updateProgressDisplay();

            // 初始化艾宾浩斯学习计划页面
            generateDraggableNumbers();

            // 尝试加载保存的数据，如果没有则使用默认数据
            if (!loadPlan()) {
                generateStudyPlanTable();
            }

            // 应用保存的折叠状态
            applyCollapsedStates();

            // 默认显示艾宾浩斯页面
            showEbbinghausPage();
        }

        function setupEventListeners() {
            // 模式切换
            modeButtons.forEach(btn => {
                btn.addEventListener('click', () => switchMode(btn.dataset.mode));
            });

            // 答案检查
            checkBtn.addEventListener('click', checkAnswer);
            nextBtn.addEventListener('click', generateNextQuestion);
            skipBtn.addEventListener('click', skipQuestion);

            // 回车键提交 - 使用事件委托
            document.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.target.classList.contains('clue-input')) {
                    if (checkBtn.style.display !== 'none') {
                        checkAnswer();
                    } else {
                        generateNextQuestion();
                    }
                }
            });

            // 搜索功能
            searchInput.addEventListener('input', performSearch);
            clearSearchBtn.addEventListener('click', clearSearch);

            // 范围选择功能
            applyRangeBtn.addEventListener('click', applyTestRange);

            // 范围输入框回车键确认
            rangeStartInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    applyTestRange();
                }
            });

            rangeEndInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    applyTestRange();
                }
            });

            // 导出导入功能
            exportBtn.addEventListener('click', exportResults);
            importBtn.addEventListener('click', () => importFile.click());
            importFile.addEventListener('change', importResults);

            // 测试模式切换
            testModeRadios.forEach(radio => {
                radio.addEventListener('change', switchTestMode);
            });

            // 艾宾浩斯页面切换
            if (gotoPracticeButton) {
                gotoPracticeButton.addEventListener('click', showPracticePage);
            }
            if (backToPlanButton) {
                backToPlanButton.addEventListener('click', showEbbinghausPage);
            }

            // 计划表功能按钮
            if (savePlanButton) {
                savePlanButton.addEventListener('click', savePlan);
            }
            if (resetPlanButton) {
                resetPlanButton.addEventListener('click', resetPlan);
            }
            if (printButton) {
                printButton.addEventListener('click', printPlan);
            }
            if (addPlanRowButton) {
                addPlanRowButton.addEventListener('click', addPlanRow);
            }
            if (removePlanRowButton) {
                removePlanRowButton.addEventListener('click', removePlanRow);
            }
        }

        function switchMode(mode) {
            currentMode = mode;

            // 更新按钮状态
            modeButtons.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.mode === mode);
            });

            // 显示对应内容
            if (mode === 'review') {
                document.getElementById('test-mode').style.display = 'none';
                document.getElementById('review-mode').style.display = 'block';
                showReviewMode();
            } else {
                document.getElementById('test-mode').style.display = 'block';
                document.getElementById('review-mode').style.display = 'none';
                generateNextQuestion();
            }
        }

        function generateNextQuestion() {
            let availableGroups;

            if (isErrorMode) {
                // 错误模式：只从错误记录中选择
                availableGroups = errorRecords.map(errorGroup =>
                    wordData.find(group => group.group === errorGroup.groupName)
                ).filter(group => group !== undefined);

                if (availableGroups.length === 0) {
                    showFeedback(false, '没有错误记录可供练习。请先进行正常测试或导入错误记录。');
                    return;
                }
            } else {
                // 正常模式：优先从当前会话错误中选择，然后从指定范围内选择
                let sessionErrorGroups = [];

                // 获取当前会话中的错误组
                if (currentSessionErrors.length > 0) {
                    sessionErrorGroups = currentSessionErrors.map(errorGroupName =>
                        wordData.find(group => group.group === errorGroupName)
                    ).filter(group => group !== undefined);
                }

                // 获取范围内的新组（未测试过的）
                const rangeGroups = wordData.filter((group, index) => {
                    const groupNumber = index + 1;
                    return groupNumber >= testRangeStart &&
                           groupNumber <= testRangeEnd &&
                           !testedSeries.has(group.group);
                });

                // 合并可用组：错误组 + 新组
                availableGroups = [...sessionErrorGroups, ...rangeGroups];

                if (availableGroups.length === 0) {
                    // 如果没有新组和错误组，检查是否所有组都已完成
                    const allRangeGroups = wordData.filter((group, index) => {
                        const groupNumber = index + 1;
                        return groupNumber >= testRangeStart && groupNumber <= testRangeEnd;
                    });

                    if (testedSeries.size >= allRangeGroups.length && currentSessionErrors.length === 0) {
                        showFeedback(true, `🎉 恭喜！您已完成所选范围内的所有系列练习！\n\n总计：${testedSeries.size} 个系列\n正确：${correctCount} 个\n需要重练：${wrongCount} 个`);
                        return;
                    } else {
                        showFeedback(false, '所选范围内没有可用的系列，请调整测试范围。');
                        return;
                    }
                }
            }

            // 随机选择一个词汇组，但优先选择错误组
            let randomGroup;
            if (!isErrorMode && currentSessionErrors.length > 0) {
                // 70% 概率选择错误组，30% 概率选择新组
                const shouldPickError = Math.random() < 0.7;
                const sessionErrorGroups = availableGroups.filter(group =>
                    currentSessionErrors.includes(group.group)
                );
                const newGroups = availableGroups.filter(group =>
                    !currentSessionErrors.includes(group.group)
                );

                if (shouldPickError && sessionErrorGroups.length > 0) {
                    randomGroup = sessionErrorGroups[Math.floor(Math.random() * sessionErrorGroups.length)];
                } else if (newGroups.length > 0) {
                    randomGroup = newGroups[Math.floor(Math.random() * newGroups.length)];
                } else {
                    randomGroup = availableGroups[Math.floor(Math.random() * availableGroups.length)];
                }
            } else {
                randomGroup = availableGroups[Math.floor(Math.random() * availableGroups.length)];
            }

            currentWordGroup = randomGroup;

            // 更新系列标题
            seriesTitleEl.textContent = randomGroup.group;

            // 生成线索区域
            cluesAreaEl.innerHTML = '';
            randomGroup.words.forEach(word => {
                const clueItem = document.createElement('div');
                clueItem.className = 'clue-item';
                clueItem.innerHTML = `
                    <span class="pos-tag">${word.pos}</span>
                    <span class="meaning">${word.meaning}</span>
                    <input type="text" class="clue-input" placeholder="请输入单词..." data-answer="${word.word.toLowerCase()}">
                `;
                cluesAreaEl.appendChild(clueItem);
            });

            // 重置按钮状态
            checkBtn.style.display = 'inline-block';
            nextBtn.style.display = 'none';
            feedbackEl.style.display = 'none';

            // 清除之前的样式
            document.querySelectorAll('.clue-input').forEach(input => {
                input.classList.remove('correct', 'incorrect');
                input.value = '';
            });

            // 聚焦第一个输入框
            const firstInput = document.querySelector('.clue-input');
            if (firstInput) firstInput.focus();

            currentQuestion++;
            updateProgress();
        }

        function checkAnswer() {
            const inputs = document.querySelectorAll('.clue-input');
            let correctAnswers = 0;
            let totalAnswers = inputs.length;
            let feedbackMessages = [];

            inputs.forEach(input => {
                const userAnswer = input.value.trim().toLowerCase();
                const correctAnswer = input.dataset.answer.toLowerCase();

                if (userAnswer === correctAnswer) {
                    input.classList.add('correct');
                    input.classList.remove('incorrect');
                    correctAnswers++;
                } else {
                    input.classList.add('incorrect');
                    input.classList.remove('correct');
                    // 显示正确答案
                    const correctWord = currentWordGroup.words.find(w => w.word.toLowerCase() === correctAnswer);
                    if (correctWord) {
                        feedbackMessages.push(`${correctWord.pos} ${correctWord.meaning} → ${correctWord.word}`);
                    }
                }
            });

            // 记录测试结果
            const testResult = {
                groupName: currentWordGroup.group,
                timestamp: new Date().toISOString(),
                correctAnswers: correctAnswers,
                totalAnswers: totalAnswers,
                isFullyCorrect: correctAnswers === totalAnswers,
                errors: []
            };

            // 记录错误的单词
            inputs.forEach(input => {
                const userAnswer = input.value.trim().toLowerCase();
                const correctAnswer = input.dataset.answer.toLowerCase();

                if (userAnswer !== correctAnswer) {
                    const correctWord = currentWordGroup.words.find(w => w.word.toLowerCase() === correctAnswer);
                    if (correctWord) {
                        testResult.errors.push({
                            userAnswer: input.value.trim(),
                            correctWord: correctWord.word,
                            pos: correctWord.pos,
                            meaning: correctWord.meaning
                        });
                    }
                }
            });

            testResults.push(testResult);

            // 记录已测试的系列
            testedSeries.add(currentWordGroup.group);

            // 更新统计
            if (correctAnswers === totalAnswers) {
                correctCount++;
                showFeedback(true, `完全正确！恭喜你掌握了 ${currentWordGroup.group} 的所有单词！`);

                // 如果是错误模式且答对了，从错误记录中移除
                if (isErrorMode) {
                    errorRecords = errorRecords.filter(record => record.groupName !== currentWordGroup.group);
                    updateErrorInfo();
                }

                // 从当前会话错误中移除（如果存在）
                const errorIndex = currentSessionErrors.indexOf(currentWordGroup.group);
                if (errorIndex > -1) {
                    currentSessionErrors.splice(errorIndex, 1);
                    showFeedback(true, `完全正确！恭喜你掌握了 ${currentWordGroup.group} 的所有单词！\n\n✅ 该系列已从重练列表中移除。`);
                }
            } else {
                wrongCount++;
                const wrongAnswerCount = totalAnswers - correctAnswers;
                let message = `答对了 ${correctAnswers}/${totalAnswers} 个单词。`;
                if (feedbackMessages.length > 0) {
                    message += `\n\n错误的答案：\n${feedbackMessages.join('\n')}`;
                }

                // 添加到当前会话错误列表（如果不是错误模式且不在列表中）
                if (!isErrorMode && !currentSessionErrors.includes(currentWordGroup.group)) {
                    currentSessionErrors.push(currentWordGroup.group);
                    message += `\n\n⚠️ 该系列将在后续练习中重新出现，直到完全掌握。`;
                }

                showFeedback(false, message);

                // 添加到错误记录（如果不是错误模式）
                if (!isErrorMode) {
                    addToErrorRecords(testResult);
                }
            }

            updateProgressDisplay();
            checkBtn.style.display = 'none';
            nextBtn.style.display = 'inline-block';
            nextBtn.focus();
        }

        function skipQuestion() {
            // 显示所有正确答案
            const answers = currentWordGroup.words.map(word =>
                `${word.pos} ${word.meaning} → ${word.word}`
            ).join('\n');

            showFeedback(false, `跳过！${currentWordGroup.group} 的所有单词：\n\n${answers}`);
            wrongCount++;
            updateStats();
            checkBtn.style.display = 'none';
            nextBtn.style.display = 'inline-block';
        }

        function showFeedback(isCorrect, message) {
            feedbackEl.innerHTML = message.replace(/\n/g, '<br>');
            feedbackEl.className = `feedback ${isCorrect ? 'correct' : 'incorrect'}`;
            feedbackEl.style.display = 'block';
        }



        function updateProgressDisplay() {
            // 计算总题数（根据当前模式）
            if (isErrorMode) {
                totalQuestions = errorRecords.length;
            } else {
                totalQuestions = testRangeEnd - testRangeStart + 1;
            }

            const answeredQuestions = correctCount + wrongCount;
            // 剩余题数 = 未测试的新题 + 当前会话中的错误题
            const newRemaining = Math.max(0, totalQuestions - testedSeries.size);
            const errorRemaining = currentSessionErrors.length;
            const remainingQuestions = newRemaining + errorRemaining;

            // 更新显示
            totalQuestionsEl.textContent = totalQuestions + (errorRemaining > 0 ? ` (+${errorRemaining}重练)` : '');
            correctQuestionsEl.textContent = correctCount;
            remainingQuestionsEl.textContent = remainingQuestions;

            // 更新进度条 - 基于已完成的不重复系列
            const completedSeries = testedSeries.size - currentSessionErrors.length;
            const progress = totalQuestions > 0 ? (completedSeries / totalQuestions) * 100 : 0;
            testProgressFill.style.width = Math.max(0, progress) + '%';
        }

        function updateProgress() {
            const progress = Math.min((currentQuestion / 20) * 100, 100);
            progressFill.style.width = progress + '%';
        }

        function getPosName(pos) {
            const posMap = {
                'n.': '名词',
                'v.': '动词', 
                'adj.': '形容词',
                'adv.': '副词',
                'prep.': '介词',
                'prep. & adv.': '介词/副词',
                'v. & n.': '动词/名词',
                'adj. & n.': '形容词/名词'
            };
            return posMap[pos] || pos;
        }

        function showReviewMode(filteredData = null) {
            const reviewContainer = document.getElementById('word-groups');
            reviewContainer.innerHTML = '';

            const dataToShow = filteredData || wordData;

            dataToShow.forEach(group => {
                const groupDiv = document.createElement('div');
                groupDiv.className = 'word-group';
                groupDiv.dataset.groupName = group.group.toLowerCase();

                const title = document.createElement('h3');
                title.textContent = group.group;
                groupDiv.appendChild(title);

                const wordList = document.createElement('div');
                wordList.className = 'word-list';

                group.words.forEach(word => {
                    const wordItem = document.createElement('div');
                    wordItem.className = 'word-item';
                    wordItem.dataset.word = word.word.toLowerCase();
                    wordItem.innerHTML = `<strong>${word.word}</strong> <em>${word.pos}</em><br><small>${word.meaning}</small>`;
                    wordList.appendChild(wordItem);
                });

                groupDiv.appendChild(wordList);
                reviewContainer.appendChild(groupDiv);
            });
        }

        function performSearch() {
            const searchTerm = searchInput.value.trim().toLowerCase();

            if (searchTerm === '') {
                clearSearch();
                return;
            }

            // 显示清除按钮
            clearSearchBtn.style.display = 'inline-block';

            // 搜索匹配的组和单词
            const matchedGroups = [];
            let totalMatches = 0;

            wordData.forEach(group => {
                const groupMatches = {
                    ...group,
                    words: [],
                    hasGroupMatch: group.group.toLowerCase().includes(searchTerm),
                    hasWordMatch: false
                };

                group.words.forEach(word => {
                    if (word.word.toLowerCase().includes(searchTerm) ||
                        word.meaning.toLowerCase().includes(searchTerm)) {
                        groupMatches.words.push(word);
                        groupMatches.hasWordMatch = true;
                        totalMatches++;
                    }
                });

                // 如果系列名匹配，包含所有单词
                if (groupMatches.hasGroupMatch) {
                    groupMatches.words = group.words;
                    totalMatches += group.words.length;
                }

                // 如果有匹配，添加到结果中
                if (groupMatches.hasGroupMatch || groupMatches.hasWordMatch) {
                    matchedGroups.push(groupMatches);
                }
            });

            // 显示搜索结果
            showReviewMode(matchedGroups);

            // 高亮匹配的内容
            highlightSearchResults(searchTerm);

            // 显示搜索统计
            const groupCount = matchedGroups.length;
            searchStats.textContent = `找到 ${groupCount} 个系列，共 ${totalMatches} 个相关单词`;
            searchStats.style.display = 'block';
        }

        function highlightSearchResults(searchTerm) {
            // 高亮系列名
            document.querySelectorAll('.word-group').forEach(group => {
                const groupName = group.dataset.groupName;
                if (groupName.includes(searchTerm)) {
                    group.classList.add('highlighted');
                    const title = group.querySelector('h3');
                    title.innerHTML = highlightText(title.textContent, searchTerm);
                }
            });

            // 高亮单词
            document.querySelectorAll('.word-item').forEach(item => {
                const word = item.dataset.word;
                const content = item.innerHTML;

                if (word.includes(searchTerm) || content.toLowerCase().includes(searchTerm)) {
                    item.classList.add('highlighted');
                    item.innerHTML = highlightText(content, searchTerm);
                }
            });
        }

        function highlightText(text, searchTerm) {
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return text.replace(regex, '<span class="highlight-text">$1</span>');
        }

        function clearSearch() {
            searchInput.value = '';
            clearSearchBtn.style.display = 'none';
            searchStats.style.display = 'none';
            showReviewMode();
        }

        function applyTestRange() {
            const start = parseInt(rangeStartInput.value) || 1;
            const end = parseInt(rangeEndInput.value) || 133;

            // 验证范围
            testRangeStart = Math.max(1, Math.min(start, 133));
            testRangeEnd = Math.max(testRangeStart, Math.min(end, 133));

            // 更新输入框值
            rangeStartInput.value = testRangeStart;
            rangeEndInput.value = testRangeEnd;

            // 重置所有统计数据
            correctCount = 0;
            wrongCount = 0;
            testedSeries.clear();
            currentSessionErrors = []; // 清空当前会话错误

            // 更新进度显示
            updateProgressDisplay();

            // 立即生成新的题目
            generateNextQuestion();

            // 显示确认消息
            showToast(`已应用范围：第${testRangeStart}-${testRangeEnd}系列`, 'success');
        }

        function addToErrorRecords(testResult) {
            // 检查是否已存在该系列的错误记录
            const existingIndex = errorRecords.findIndex(record => record.groupName === testResult.groupName);

            if (existingIndex >= 0) {
                // 更新现有记录
                errorRecords[existingIndex] = {
                    groupName: testResult.groupName,
                    lastError: testResult.timestamp,
                    errorCount: errorRecords[existingIndex].errorCount + 1,
                    errors: testResult.errors
                };
            } else {
                // 添加新记录
                errorRecords.push({
                    groupName: testResult.groupName,
                    lastError: testResult.timestamp,
                    errorCount: 1,
                    errors: testResult.errors
                });
            }

            updateErrorInfo();
        }

        function updateErrorInfo() {
            errorCount.textContent = errorRecords.length;
            if (errorRecords.length > 0) {
                errorInfo.style.display = 'block';
            } else {
                errorInfo.style.display = 'none';
            }
        }

        function switchTestMode() {
            const selectedMode = document.querySelector('input[name="test-mode"]:checked').value;
            isErrorMode = selectedMode === 'errors';

            // 重置所有统计数据
            correctCount = 0;
            wrongCount = 0;
            testedSeries.clear();
            currentSessionErrors = []; // 清空当前会话错误

            if (isErrorMode) {
                rangeSelector.style.display = 'none';
                errorInfo.style.display = 'block';
            } else {
                rangeSelector.style.display = 'block';
                errorInfo.style.display = errorRecords.length > 0 ? 'block' : 'none';
            }

            // 更新进度显示
            updateProgressDisplay();
        }

        function exportResults() {
            const exportData = {
                version: '1.0',
                exportDate: new Date().toISOString(),
                testResults: testResults,
                errorRecords: errorRecords,
                statistics: {
                    totalQuestions: correctCount + wrongCount,
                    correctCount: correctCount,
                    wrongCount: wrongCount,
                    accuracy: correctCount + wrongCount > 0 ? Math.round((correctCount / (correctCount + wrongCount)) * 100) : 0
                }
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `词性转换测试结果_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showToast('测试结果已导出！', 'success');
        }

        function importResults() {
            const file = importFile.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importData = JSON.parse(e.target.result);

                    if (!importData.version || !importData.testResults || !importData.errorRecords) {
                        throw new Error('文件格式不正确');
                    }

                    // 合并测试结果
                    testResults = testResults.concat(importData.testResults);

                    // 合并错误记录
                    importData.errorRecords.forEach(importedError => {
                        const existingIndex = errorRecords.findIndex(record => record.groupName === importedError.groupName);

                        if (existingIndex >= 0) {
                            // 如果导入的记录更新，则替换
                            if (new Date(importedError.lastError) > new Date(errorRecords[existingIndex].lastError)) {
                                errorRecords[existingIndex] = importedError;
                            }
                        } else {
                            errorRecords.push(importedError);
                        }
                    });

                    updateErrorInfo();
                    showToast(`成功导入 ${importData.testResults.length} 条测试记录和 ${importData.errorRecords.length} 条错误记录！`, 'success');

                } catch (error) {
                    showToast('导入失败：文件格式错误或数据损坏', 'error');
                    console.error('Import error:', error);
                }
            };

            reader.readAsText(file);
            importFile.value = ''; // 清空文件选择
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => toast.classList.add('show'), 100);

            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // --- 艾宾浩斯学习计划功能 ---

        // 页面切换功能
        function showEbbinghausPage() {
            ebbinghausHomepage.style.display = 'block';
            practicePage.style.display = 'none';
            currentPage = 'ebbinghaus';
        }

        function showPracticePage() {
            ebbinghausHomepage.style.display = 'none';
            practicePage.style.display = 'block';
            currentPage = 'practice';
            // 进入练习页面时生成题目
            generateNextQuestion();
        }

        // 生成可拖拽的学习单元
        function generateDraggableNumbers() {
            const container = document.getElementById('draggable-numbers-container');
            if (!container) return;

            container.innerHTML = '';

            wordGroups.forEach(group => {
                const numberDiv = document.createElement('div');
                numberDiv.className = 'draggable-number';
                numberDiv.draggable = true;
                numberDiv.dataset.groupNumber = group.number;
                numberDiv.innerHTML = `<div>第${group.number}组</div><div style="font-size: 0.8em; margin-top: 2px;">${group.range}</div>`;
                numberDiv.title = group.description;

                // 拖拽事件
                numberDiv.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', group.number);
                    e.dataTransfer.effectAllowed = 'copy';
                });

                // 点击直接进入练习
                numberDiv.addEventListener('click', () => {
                    numberDiv.classList.add('clicked');
                    setTimeout(() => numberDiv.classList.remove('clicked'), 300);

                    // 设置练习范围并进入练习
                    const [start, end] = group.range.split('-').map(Number);
                    testRangeStart = start;
                    testRangeEnd = end;

                    // 重置统计
                    correctCount = 0;
                    wrongCount = 0;
                    testedSeries.clear();
                    currentSessionErrors = [];

                    updateProgressDisplay();
                    showPracticePage();
                    showToast(`开始练习第${group.number}组 (${group.range}系列)`, 'success');
                });

                container.appendChild(numberDiv);
            });
        }

        // 生成学习计划表
        function generateStudyPlanTable() {
            const tbody = document.querySelector('#study-plan-table tbody');
            if (!tbody) return;

            tbody.innerHTML = ''; // 清空现有内容

            // 生成30行计划表，每10行一组
            const totalRows = 30;
            for (let groupIndex = 0; groupIndex < Math.ceil(totalRows / rowsPerGroup); groupIndex++) {
                const groupStart = groupIndex * rowsPerGroup;
                const groupEnd = Math.min(groupStart + rowsPerGroup, totalRows);
                const groupNumber = groupIndex + 1;

                // 创建组头
                const groupHeaderTr = createGroupHeader(groupNumber, groupStart, groupEnd);
                tbody.appendChild(groupHeaderTr);

                // 创建组内的行
                for (let i = groupStart; i < groupEnd; i++) {
                    const rowNumber = i + 1;
                    const tr = createPlanRowUI(rowNumber, groupNumber);
                    tr.classList.add(`group-${groupNumber}-row`);
                    tbody.appendChild(tr);
                }
            }

            // 添加拖拽功能
            setupDragAndDrop();
        }

        // 创建组头
        function createGroupHeader(groupNumber, startIndex, endIndex) {
            const tr = document.createElement('tr');
            tr.className = 'group-header-row group-header';
            tr.setAttribute('data-group', groupNumber);

            // 折叠按钮
            const foldCell = document.createElement('td');
            const collapseToggle = document.createElement('div');
            collapseToggle.className = 'collapse-toggle';
            collapseToggle.textContent = '▼';
            collapseToggle.dataset.group = groupNumber;
            collapseToggle.title = '折叠/展开';
            collapseToggle.addEventListener('click', () => toggleGroup(groupNumber));
            foldCell.appendChild(collapseToggle);
            tr.appendChild(foldCell);

            // 组信息
            const infoCell = document.createElement('td');
            infoCell.colSpan = 10;
            infoCell.textContent = `第${groupNumber}组 (第${startIndex + 1}-${endIndex}学习日)`;
            infoCell.style.textAlign = 'center';
            infoCell.style.fontWeight = 'bold';
            tr.appendChild(infoCell);

            return tr;
        }

        // 创建计划表行UI
        function createPlanRowUI(rowNumber, groupNumber) {
            const tr = document.createElement('tr');

            // 空的折叠列（组内行不需要折叠按钮）
            const emptyCell = document.createElement('td');
            emptyCell.textContent = '—';
            emptyCell.style.color = '#ccc';
            tr.appendChild(emptyCell);

            // 序号列
            const seqCell = document.createElement('td');
            seqCell.textContent = rowNumber;
            tr.appendChild(seqCell);

            // 学习日期列
            const dateCell = document.createElement('td');
            const dateInput = document.createElement('input');
            dateInput.type = 'text';
            dateInput.className = 'plan-date-input';
            dateInput.placeholder = '如: 1201';
            dateInput.maxLength = 6;
            dateInput.style.width = '100%';
            dateInput.style.border = '1px solid #666666';
            dateInput.style.padding = '6px 8px';
            dateInput.style.borderRadius = '4px';
            dateInput.style.fontSize = '0.9em';
            dateInput.style.textAlign = 'center';
            dateInput.style.background = '#2d2d2d';
            dateInput.style.color = '#e0e0e0';

            // 只允许输入数字
            dateInput.addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');
                if (this.value.length > 6) {
                    this.value = this.value.slice(0, 6);
                }
            });

            dateCell.appendChild(dateInput);
            tr.appendChild(dateCell);

            // 学习内容列
            const contentCell = document.createElement('td');
            contentCell.className = 'plan-cell';
            contentCell.dataset.type = 'content';
            const content = document.createElement('div');
            content.className = 'plan-cell-content';
            contentCell.appendChild(content);
            tr.appendChild(contentCell);

            // 复习列 - 每列一个复习时间点
            const reviewDays = ['day1', 'day2', 'day3', 'day5', 'day8', 'day16', 'day31'];
            reviewDays.forEach(dayType => {
                const cell = document.createElement('td');
                cell.className = 'review-cell';
                cell.dataset.type = dayType;

                // 输入框
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'review-input';
                input.placeholder = '单元号';
                input.maxLength = 6;

                // 只允许输入数字和逗号
                input.addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^0-9,]/g, '');
                });

                // 完成按钮
                const completeBtn = document.createElement('button');
                completeBtn.className = 'complete-btn';
                completeBtn.textContent = '完成';
                completeBtn.dataset.day = dayType;
                completeBtn.dataset.row = rowNumber;

                // 状态显示
                const status = document.createElement('div');
                status.className = 'review-status';
                status.textContent = '未完成';

                // 完成按钮点击事件
                completeBtn.addEventListener('click', function() {
                    toggleReviewComplete(this, input, status);
                });

                cell.appendChild(input);
                cell.appendChild(completeBtn);
                cell.appendChild(status);
                tr.appendChild(cell);
            });

            return tr;
        }

        // 设置拖拽功能
        function setupDragAndDrop() {
            const planCells = document.querySelectorAll('.plan-cell');

            planCells.forEach(cell => {
                cell.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    cell.classList.add('drag-over');
                });

                cell.addEventListener('dragleave', () => {
                    cell.classList.remove('drag-over');
                });

                cell.addEventListener('drop', (e) => {
                    e.preventDefault();
                    cell.classList.remove('drag-over');

                    const groupNumber = e.dataTransfer.getData('text/plain');
                    if (groupNumber) {
                        addGroupToCell(cell, groupNumber);
                    }
                });
            });
        }

        // 添加组到单元格
        function addGroupToCell(cell, groupNumber) {
            const content = cell.querySelector('.plan-cell-content');

            // 检查是否已存在
            const existing = content.querySelector(`[data-group="${groupNumber}"]`);
            if (existing) return;

            const groupItem = document.createElement('span');
            groupItem.className = 'plan-cell-item';
            groupItem.dataset.group = groupNumber;
            groupItem.textContent = `第${groupNumber}组`;
            groupItem.title = wordGroups.find(g => g.number == groupNumber)?.description || '';

            // 点击进入练习
            groupItem.addEventListener('click', (e) => {
                e.stopPropagation();
                const group = wordGroups.find(g => g.number == groupNumber);
                if (group) {
                    const [start, end] = group.range.split('-').map(Number);
                    testRangeStart = start;
                    testRangeEnd = end;

                    correctCount = 0;
                    wrongCount = 0;
                    testedSeries.clear();
                    currentSessionErrors = [];

                    updateProgressDisplay();
                    showPracticePage();
                    showToast(`开始练习第${groupNumber}组`, 'success');
                }
            });

            // 双击删除
            groupItem.addEventListener('dblclick', (e) => {
                e.stopPropagation();
                content.removeChild(groupItem);
            });

            content.appendChild(groupItem);
        }

        // 切换复习完成状态
        function toggleReviewComplete(button, input, status) {
            const isCompleted = button.classList.contains('completed');

            if (isCompleted) {
                // 取消完成
                button.classList.remove('completed');
                button.textContent = '完成';
                status.textContent = '未完成';
                status.classList.remove('completed');
                input.disabled = false;
                input.style.opacity = '1';
            } else {
                // 标记完成
                if (input.value.trim() === '') {
                    showToast('请先输入要复习的单元号！', 'error');
                    return;
                }

                button.classList.add('completed');
                button.textContent = '已完成';
                status.textContent = '已完成';
                status.classList.add('completed');
                input.disabled = true;
                input.style.opacity = '0.7';
            }

            // 保存状态
            savePlan();
        }

        // 切换组的折叠状态
        function toggleGroup(groupNumber) {
            console.log('toggleGroup 被调用，组号:', groupNumber);
            const groupRows = document.querySelectorAll(`.group-${groupNumber}-row`);
            const collapseToggle = document.querySelector(`.collapse-toggle[data-group="${groupNumber}"]`);

            console.log('找到的组行数:', groupRows.length);
            console.log('找到的折叠按钮:', collapseToggle);

            if (groupRows.length > 0 && collapseToggle) {
                const isCollapsed = collapseToggle.classList.contains('collapsed');
                console.log('当前是否折叠:', isCollapsed);

                if (isCollapsed) {
                    // 展开
                    console.log('执行展开操作');
                    groupRows.forEach(row => row.classList.remove('row-group-collapsed'));
                    collapseToggle.classList.remove('collapsed');
                    collapseToggle.textContent = '▼';
                    collapseStates[groupNumber] = false;
                } else {
                    // 折叠
                    console.log('执行折叠操作');
                    groupRows.forEach(row => row.classList.add('row-group-collapsed'));
                    collapseToggle.classList.add('collapsed');
                    collapseToggle.textContent = '▶';
                    collapseStates[groupNumber] = true;
                }
            } else {
                console.error('无法找到组行或折叠按钮');
            }
        }

        // 应用保存的折叠状态
        function applyCollapsedStates() {
            console.log('应用折叠状态:', collapseStates);
            Object.keys(collapseStates).forEach(groupNumber => {
                if (collapseStates[groupNumber] === true) {
                    const groupRows = document.querySelectorAll(`.group-${groupNumber}-row`);
                    const collapseToggle = document.querySelector(`.collapse-toggle[data-group="${groupNumber}"]`);

                    if (groupRows.length > 0 && collapseToggle) {
                        // 应用折叠状态
                        groupRows.forEach(row => row.classList.add('row-group-collapsed'));
                        collapseToggle.classList.add('collapsed');
                        collapseToggle.textContent = '▶';
                    }
                }
            });
        }

        // 保存计划
        function savePlan() {
            console.log('savePlan 函数被调用了！');
            try {
                const planData = {
                    tableData: [],
                    collapseStates: collapseStates,
                    timestamp: new Date().toISOString()
                };

                // 获取所有非组头的行
                const dataRows = document.querySelectorAll('#study-plan-table tbody tr:not(.group-header-row)');
                dataRows.forEach((tr, index) => {
                    const rowData = {
                        seq: parseInt(tr.cells[1].textContent),
                        date: tr.cells[2].querySelector('.plan-date-input').value || '',
                        studyContent: [],
                        day1: [],
                        day2: [],
                        day3: [],
                        day5: [],
                        day8: [],
                        day16: [],
                        day31: []
                    };

                    // 获取学习内容（第4列，索引3）
                    const contentCell = tr.cells[3];
                    if (contentCell) {
                        const groups = Array.from(contentCell.querySelectorAll('.plan-cell-item')).map(item => item.dataset.group);
                        rowData.studyContent = groups;
                    }

                    // 获取各个复习时间点的内容（第5-11列，索引4-10）
                    const reviewDays = ['day1', 'day2', 'day3', 'day5', 'day8', 'day16', 'day31'];
                    reviewDays.forEach((dayType, index) => {
                        const cell = tr.cells[index + 4]; // 从第5列开始
                        if (cell) {
                            const input = cell.querySelector('.review-input');
                            const button = cell.querySelector('.complete-btn');
                            rowData[dayType] = {
                                content: input ? input.value : '',
                                completed: button ? button.classList.contains('completed') : false
                            };
                        }
                    });

                    planData.tableData.push(rowData);
                });

                localStorage.setItem(planTableStorageKey, JSON.stringify(planData));
                showToast('学习计划已保存！', 'success');
                console.log('保存成功，数据:', planData);
            } catch (error) {
                console.error('保存失败:', error);
                showToast('保存失败：' + error.message, 'error');
            }
        }

        // 加载计划
        function loadPlan() {
            try {
                const savedDataString = localStorage.getItem(planTableStorageKey);
                if (savedDataString) {
                    console.log('找到保存的数据，长度:', savedDataString.length);
                    const savedData = JSON.parse(savedDataString);

                    // 恢复折叠状态
                    if (savedData.collapseStates) {
                        collapseStates = savedData.collapseStates;
                    }

                    // 使用保存的表格数据，如果没有则使用默认结构
                    const tableData = savedData.tableData && savedData.tableData.length > 0
                                    ? savedData.tableData
                                    : Array.from({length: 30}, (_, i) => ({seq: i + 1, date: ""}));

                    generateStudyPlanTableWithData(tableData, savedData);
                    return true;
                } else {
                    console.log('没有找到保存的数据');
                    return false;
                }
            } catch (error) {
                console.error('加载数据时出错:', error);
                return false;
            }
        }

        // 使用数据生成学习计划表
        function generateStudyPlanTableWithData(planDataArray, savedPlanObject = null) {
            const tbody = document.querySelector('#study-plan-table tbody');
            if (!tbody) return;

            console.log('生成学习计划表，行数:', planDataArray.length);
            tbody.innerHTML = '';

            // 按组生成表格
            for (let groupIndex = 0; groupIndex < Math.ceil(planDataArray.length / rowsPerGroup); groupIndex++) {
                const groupStart = groupIndex * rowsPerGroup;
                const groupEnd = Math.min(groupStart + rowsPerGroup, planDataArray.length);
                const groupNumber = groupIndex + 1;

                // 创建组头
                const groupHeaderTr = createGroupHeader(groupNumber, groupStart, groupEnd);
                tbody.appendChild(groupHeaderTr);

                // 创建组内的行
                for (let i = groupStart; i < groupEnd; i++) {
                    // 如果有保存的数据，使用保存的数据，否则使用默认数据
                    const specificSavedDataForRow = (savedPlanObject && savedPlanObject.tableData && savedPlanObject.tableData[i])
                                                  ? savedPlanObject.tableData[i]
                                                  : planDataArray[i];
                    const tr = createPlanRowUIWithData(planDataArray[i], i, groupNumber, specificSavedDataForRow);
                    tr.classList.add(`group-${groupNumber}-row`);
                    tbody.appendChild(tr);
                }
            }

            // 添加拖拽功能
            setupDragAndDrop();
        }

        // 创建带数据的计划表行UI
        function createPlanRowUIWithData(rowData, rowIndex, groupNumber, savedRowData = null) {
            const tr = createPlanRowUI(rowData.seq, groupNumber);

            // 设置日期
            const dateInput = tr.querySelector('.plan-date-input');
            if (dateInput && savedRowData && savedRowData.date) {
                dateInput.value = savedRowData.date;
            }

            // 恢复学习内容
            if (savedRowData && savedRowData.studyContent) {
                const contentCell = tr.cells[3]; // 学习内容列
                if (contentCell && Array.isArray(savedRowData.studyContent) && savedRowData.studyContent.length > 0) {
                    savedRowData.studyContent.forEach(groupNumber => {
                        addGroupToCell(contentCell, groupNumber);
                    });
                }
            }

            // 恢复各个复习时间点的内容
            if (savedRowData) {
                const reviewDays = ['day1', 'day2', 'day3', 'day5', 'day8', 'day16', 'day31'];
                reviewDays.forEach((dayType, index) => {
                    const cell = tr.cells[index + 4]; // 从第5列开始
                    const reviewData = savedRowData[dayType];

                    if (cell && reviewData && typeof reviewData === 'object') {
                        const input = cell.querySelector('.review-input');
                        const button = cell.querySelector('.complete-btn');
                        const status = cell.querySelector('.review-status');

                        if (input && reviewData.content) {
                            input.value = reviewData.content;
                        }

                        if (button && status && reviewData.completed) {
                            button.classList.add('completed');
                            button.textContent = '已完成';
                            status.textContent = '已完成';
                            status.classList.add('completed');
                            if (input) {
                                input.disabled = true;
                                input.style.opacity = '0.7';
                            }
                        }
                    }
                });
            }

            return tr;
        }

        // 重置计划
        function resetPlan() {
            if (confirm('确定要重置学习计划吗？这将清除所有已设置的内容。')) {
                localStorage.removeItem(planTableStorageKey);
                collapseStates = {};
                document.querySelector('#study-plan-table tbody').innerHTML = '';
                generateStudyPlanTable();
                showToast('学习计划已重置！', 'success');
            }
        }

        // 打印计划
        function printPlan() {
            window.print();
        }

        // 添加计划行
        function addPlanRow() {
            console.log('addPlanRow 函数被调用了！');
            const tbody = document.querySelector('#study-plan-table tbody');

            // 获取当前所有非组头行的数量
            const currentDataRows = tbody.querySelectorAll('tr:not(.group-header-row)');
            const newSeq = currentDataRows.length + 1;

            console.log('当前数据行数:', currentDataRows.length, '新序号:', newSeq);

            // 创建新行
            const newRow = createPlanRowUI(newSeq);

            // 确定应该添加到哪个组
            const groupNumber = Math.ceil(newSeq / rowsPerGroup);
            newRow.classList.add(`group-${groupNumber}-row`);

            // 检查是否需要创建新的组头
            const existingGroupHeader = tbody.querySelector(`.group-header[data-group="${groupNumber}"]`);

            if (!existingGroupHeader) {
                // 需要创建新的组头
                const groupStart = (groupNumber - 1) * rowsPerGroup;
                const groupEnd = Math.min(groupStart + rowsPerGroup, newSeq);
                const groupHeaderTr = createGroupHeader(groupNumber, groupStart, groupEnd);
                tbody.appendChild(groupHeaderTr);
            }

            // 添加新行
            tbody.appendChild(newRow);

            // 重新设置拖拽功能
            setupDragAndDrop();

            showToast('已添加新的学习日！', 'success');
            console.log('新行添加完成');
        }

        // 删除计划行
        function removePlanRow() {
            console.log('removePlanRow 函数被调用了！');
            const tbody = document.querySelector('#study-plan-table tbody');

            // 获取所有非组头行
            const dataRows = tbody.querySelectorAll('tr:not(.group-header-row)');

            if (dataRows.length > 1) {
                const lastRow = dataRows[dataRows.length - 1];
                const lastRowSeq = parseInt(lastRow.cells[1].textContent);
                const groupNumber = Math.ceil(lastRowSeq / rowsPerGroup);

                console.log('删除行序号:', lastRowSeq, '所属组:', groupNumber);

                // 删除最后一行
                tbody.removeChild(lastRow);

                // 检查该组是否还有其他行
                const remainingRowsInGroup = tbody.querySelectorAll(`.group-${groupNumber}-row`);

                if (remainingRowsInGroup.length === 0) {
                    // 如果该组没有行了，删除组头
                    const groupHeader = tbody.querySelector(`.group-header[data-group="${groupNumber}"]`);
                    if (groupHeader) {
                        tbody.removeChild(groupHeader);
                        console.log('删除了空组头:', groupNumber);
                    }
                }

                showToast('已删除最后一个学习日！', 'success');
            } else {
                showToast('至少需要保留一个学习日！', 'error');
            }
        }
    </script>
</body>
</html>
